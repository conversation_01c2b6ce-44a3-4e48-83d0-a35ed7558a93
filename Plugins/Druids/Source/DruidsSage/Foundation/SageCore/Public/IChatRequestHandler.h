#pragma once

#include <CoreMinimal.h>

class UIDruidsSageChatItem;

class IChatRequestHandler
{
public:
    virtual ~IChatRequestHandler() = default;

    virtual bool IsNoActiveRequest() const = 0;
    virtual void StopAndCleanupRequest(TArray<UIDruidsSageChatItem*> ChatItems) = 0;
    virtual void SetupAndSendRequest(TArray<UIDruidsSageChatItem*> ChatItems,
                                     UIDruidsSageChatItem* AssistantMessage, const FString& Context) = 0;
};