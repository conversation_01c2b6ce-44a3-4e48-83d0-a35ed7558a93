using UnrealBuildTool;

public class SageNetworking : ModuleRules
{
    public SageNetworking(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                
                "HTTP",
                
                "Json",
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",

                "SageCommonTypes",
                "SageCore",

                "SageJSONUtilities",
                "SagePython",
            }
        );
        
                
        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                }
            );
        }
    }
}