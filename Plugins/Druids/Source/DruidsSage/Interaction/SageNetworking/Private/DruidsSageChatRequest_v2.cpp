#include "DruidsSageChatRequest_v2.h"

#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/ScopeTryLock.h>
#include <Async/Async.h>
#include <HttpModule.h>

#include "LogDruids.h"

#include "DruidsSageHelper.h"
#include "DruidsSageSettings.h"

#include "ISageExtensionDelegator.h"
#include "SimpleJSON.h"
#include "SagePythonWorker.h"

#if WITH_EDITOR
#include <Editor.h>
#endif

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageChatRequest_v2)
#endif

FString SageURL_v7a("https://groveserver.replit.app/sprout/sage/v7a");
FString SageURL_v7b("https://groveserver.replit.app/sprout/sage/v7b");
FString SageURL_v8("https://groveserver.replit.app/sprout/sage/v8");
FString SageURL = SageURL_v7b;

FString Environment_Staging("staging");
FString Environment_Production("production");
FString Environment = Environment_Production;

#if WITH_EDITOR
UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::EditorTask(const TArray<FDruidsSageChatMessage>& Messages,
                                                                 const TSharedPtr<ISageExtensionDelegator> ExtensionDelegator,
                                                                 const FString& UserFocusContext)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = SendMessages(
		GEditor->GetEditorWorldContext().World(), Messages, ExtensionDelegator, UserFocusContext);
	
	NewAsyncTask->bIsEditorTask = true;

	return NewAsyncTask;
}
#endif

UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::SendMessages(
	UObject* const WorldContextObject,
	const TArray<FDruidsSageChatMessage>& Messages,
	const TSharedPtr<ISageExtensionDelegator> ExtensionDelegator,
	const FString& UserFocusContext)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = NewObject<UDruidsSageChatRequest_v2>();
	NewAsyncTask->Messages = Messages;
	NewAsyncTask->ExtensionDelegator = ExtensionDelegator;
	NewAsyncTask->UserFocusContext = UserFocusContext;

	NewAsyncTask->RegisterWithGameInstance(WorldContextObject);

	return NewAsyncTask;
}

bool UDruidsSageChatRequest_v2::CanActivateTask()
{
	if (!Super::CanActivateTask())
	{
		return false;
	}

	if (Messages.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Can't activate task: Invalid Messages."), *FString(__FUNCTION__), GetUniqueID());
		return false;
	}

	return true;
}

bool UDruidsSageChatRequest_v2::CanBindProgress() const
{
	//Always streaming
	return true;
}

FString UDruidsSageChatRequest_v2::GetEndpointURL() const
{
	return SageURL;
}

void UDruidsSageChatRequest_v2::CustomizeRequestHeaders()
{
	Super::CustomizeRequestHeaders();

	if (HttpRequest.IsValid())
	{
		HttpRequest.Get()->SetHeader(TEXT("environment"), Environment);
	}
}

FString UDruidsSageChatRequest_v2::SetRequestContent()
{
	FScopeLock Lock(&Mutex);

	if (!HttpRequest.IsValid())
	{
		return FString();
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Mounting content"), *FString(__FUNCTION__), GetUniqueID());

	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	if (UserFocusContext.Len() > 0)
	{
		JsonRequest->SetStringField("user_focus_context", UserFocusContext);
	}

	TArray<TSharedPtr<FJsonValue>> MessagesJson;
	for (const FDruidsSageChatMessage& Iterator : Messages)
	{
		MessagesJson.Add(Iterator.GetMessageJson());
	}

	JsonRequest->SetArrayField("messages", MessagesJson);

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);
	FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer);

	HttpRequest->SetContentAsString(RequestContentString);

	return RequestContentString;
}

void UDruidsSageChatRequest_v2::OnProgressUpdated(const FString& Content, int32 BytesSent, int32 BytesReceived)
{
	FScopeLock Lock(&Mutex);

	if (Content.IsEmpty())
	{
		return;
	}

	TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Progress Updated"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: %s; Bytes Sent: %d; Bytes Received: %d"), *FString(__FUNCTION__), GetUniqueID(),
	       StreamedResponses.IsEmpty() ? TEXT("<none>") : *StreamedResponses.Top(), BytesSent, BytesReceived);

	ProcessStreamedResponses(StreamedResponses);

	if (!Response.bSuccess)
	{
		return;
	}

	if (!bInitialized)
	{
		bInitialized = true;

		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ProgressStarted.Broadcast(Response);
		});
	}

	AsyncTask(ENamedThreads::GameThread, [this]
	{
		FScopeTryLock Lock(&Mutex);

		if (Lock.IsLocked())
		{
			ProgressUpdated.Broadcast(Response);
		}
	});
}

void UDruidsSageChatRequest_v2::OnProgressCompleted(const FString& Content, const bool bWasSuccessful)
{
	FScopeLock Lock(&Mutex);

	if (!bWasSuccessful || Content.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			RequestFailed.Broadcast();
		});

		return;
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Process Completed"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: \nBEGIN>>>\n%s\n<<<END"), *FString(__FUNCTION__), GetUniqueID(), *Content);

	const TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);
	ProcessStreamedResponses(StreamedResponses);

	if (Response.bSuccess)
	{
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);

			ProcessCompleted.Broadcast(Response);
		});
	}
	else
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ErrorReceived.Broadcast(Response);
		});
	}
}

TArray<FString> UDruidsSageChatRequest_v2::GetStreamedResponsesFromContent(const FString& Content)
{
	TArray<FString> Deltas_In, Deltas_Out;
	Content.ParseIntoArray(Deltas_In, TEXT("\n\n"));
	for (FString Delta_In : Deltas_In)
	{
		if (Delta_In.StartsWith("data:"))
		{
			if (!Delta_In.StartsWith("data: [done]"))
			{
				Deltas_Out.Add(Delta_In.Replace(TEXT("data: "), TEXT("")));
			}
		}
	}
	
	return Deltas_Out;
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponses(const TArray<FString>& StreamedResponses)
{
	FScopeLock Lock(&Mutex);

	Response.bSuccess = true;
	
	Response.Choices.Empty(StreamedResponses.Num());
	for (const FString& StreamedResponse : StreamedResponses)
	{
		ProcessStreamedResponse(StreamedResponse);
	}
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponse(const FString& StreamedResponse)
{
	FScopeLock Lock(&Mutex);

	if (StreamedResponse.IsEmpty())
	{
		return;
	}

	SimpleJSON ResponseJSON(StreamedResponse);

	if (ResponseJSON["choices"].IsEmpty())
	{
		if (TArray<TSharedPtr<FJsonValue>> QueryRequests = ResponseJSON["query_requests"].AsNativeArray(); !QueryRequests.IsEmpty())
		{
			ProcessQueryRequests(&QueryRequests);
			return;
		}

		if (TArray<TSharedPtr<FJsonValue>> PythonCompileRequests = ResponseJSON["python_compilation_requests"].AsNativeArray(); !PythonCompileRequests.IsEmpty())
		{
			ProcessPythonCompileRequests(&PythonCompileRequests);
			return;
		}

		if (TArray<TSharedPtr<FJsonValue>> PythonExecuteRequests = ResponseJSON["python_execute_requests"].AsNativeArray(); !PythonExecuteRequests.IsEmpty())
		{
			ProcessPythonExecuteRequests(&PythonExecuteRequests);
			return;
		}

		Response.bSuccess = false;
		return;
	}

	for (int i = 0; i < ResponseJSON["choices"].Size(); i++)
	{
		SimpleJSON ChoiceJSON = ResponseJSON["choices"][i];

		int32 ChoiceIndex = 0;
		if (ChoiceJSON.IsObject())
		{
			ChoiceIndex = static_cast<int32>(ChoiceJSON["index"]);
		}

		FDruidsSageChatChoice* Choice = Response.Choices.FindByPredicate([this, ChoiceIndex](const FDruidsSageChatChoice& Element)
		{
			return Element.Index == ChoiceIndex;
		});

		if (!Choice)
		{
			FDruidsSageChatChoice NewChoice;
			NewChoice.Index = ChoiceIndex;
			Choice = &Response.Choices.Add_GetRef(NewChoice);
		}

		if (ChoiceJSON.HasKey("message") && ChoiceJSON["message"].IsObject())
		{
			FString RoleString = static_cast<FString>(ChoiceJSON["message"]["role"]);
			Choice->Message.SetRole(RoleString == "user" ? EDruidsSageChatRole::User : EDruidsSageChatRole::Assistant);

			if (ChoiceJSON["message"].HasKey("thinking") && ChoiceJSON["message"]["thinking"].IsArray())
			{
				TArray<TSharedPtr<FJsonValue>> ThinkingArray = ChoiceJSON["message"]["thinking"].AsNativeArray();
				Choice->Message.SetThinkingArray(ThinkingArray);
			}
			
			if (ChoiceJSON["message"]["content"].IsString())
			{
				FString ContentString = static_cast<FString>(ChoiceJSON["message"]["content"]);
				Choice->Message.SetChatContent(ContentString);
			}
			else if (ChoiceJSON["message"]["content"].IsArray())
			{
				TArray<TSharedPtr<FJsonValue>> ContentArray = ChoiceJSON["message"]["content"].AsNativeArray(); 
				Choice->Message.SetContentArray(ContentArray);
			}
		}
		else if (ChoiceJSON.HasKey("delta") && ChoiceJSON["delta"].IsObject())
		{
			FString RoleString = static_cast<FString>(ChoiceJSON["delta"]["role"]);
			Choice->Message.SetRole(UDruidsSageHelper::NameToRole(*RoleString));

			FString ContentString = static_cast<FString>(ChoiceJSON["delta"]["content"]);
			Choice->Message.SetChatContent(Choice->Message.GetChatContent() + ContentString);
		}
		else if (ChoiceJSON.HasKey("text"))
		{
			FString MessageString = static_cast<FString>(ChoiceJSON["text"]);
			Choice->Message.SetRole(EDruidsSageChatRole::Assistant);
			Choice->Message.SetChatContent(Choice->Message.GetChatContent() + MessageString);
		}

		while (Choice->Message.GetChatContent().StartsWith("\n"))
		{
			Choice->Message.SetChatContent(Choice->Message.GetChatContent().RightChop(1));
		}

		if (ChoiceJSON.HasKey("finish_reason"))
		{
			Choice->FinishReason = *static_cast<FString>(ChoiceJSON["finish_reason"]);
		}
	}
}

void UDruidsSageChatRequest_v2::ProcessQueryRequests(const TArray<TSharedPtr<FJsonValue>>* QueryRequestsArray)
{
	FString QueryResponseUrl;
	TArray<TSharedPtr<FJsonObject>> QueryResponses;

	/**
	 * Call the pending query requests
	 */
	for (auto Iterator = QueryRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> QueryRequestObj = (*Iterator)->AsObject(); QueryRequestObj.IsValid())
		{
			if (FString QueryRequestId; QueryRequestObj->TryGetStringField(TEXT("query_request_id"), QueryRequestId))
			{
				if (!QueryResponsesDone.Contains(QueryRequestId))
				{
					TSharedPtr<FJsonObject> ResultsObject = ExtensionDelegator->OnQueryRequested(QueryRequestObj);

					QueryResponsesDone.Add(QueryRequestId);
					QueryResponses.Add(QueryRequestObj);
					
					if (FString ResponseUrlToUse; QueryResponseUrl.IsEmpty() && QueryRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						QueryResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up query responses and send back
	 */
	if (!QueryResponses.IsEmpty())
	{
		//
		// Set up the request
		//
		HttpRequest = FHttpModule::Get().CreateRequest();
		{
			HttpRequest->SetURL(QueryResponseUrl);
			HttpRequest->SetVerb("POST");
			HttpRequest->SetHeader("Content-Type", "application/json");
	
			// Use the DruidsUserToken as the value for the Authorization header
			FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
			HttpRequest->SetHeader("Authorization", AuthHeader);
		}

		//
		// Store the request while it is in flight
		//
		TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
		{
			FScopeLock Lock(&InFlightQueryMutex);

			// Store the request to keep it alive until completion
			StoredRequest = HttpRequest;
			InFlightQueryResponses.Add(StoredRequest);
		}

		//
		// Add the query responses
		//
		{
			// Create a JSON object to hold the query responses
			TSharedPtr<FJsonObject> RequestBodyObj = MakeShared<FJsonObject>();
			
			// Create an array of query response objects
			TArray<TSharedPtr<FJsonValue>> QueryResponsesArray;
			for (const TSharedPtr<FJsonObject>& QueryResponse : QueryResponses)
			{
				QueryResponsesArray.Add(MakeShared<FJsonValueObject>(QueryResponse));
			}
			
			// Add the array to the JSON object
			RequestBodyObj->SetArrayField(TEXT("query_responses"), QueryResponsesArray);
			
			// Serialize the JSON object to a string
			FString RequestBodyString;
			TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
			FJsonSerializer::Serialize(RequestBodyObj.ToSharedRef(), Writer);
			
			// Set the request content
			HttpRequest->SetContentAsString(RequestBodyString);
		}

		//
		// Fire the request off
		//
		{
			// Add completion handler to clean up the reference when done
			HttpRequest->OnProcessRequestComplete().BindLambda(
				[this, StoredRequest]
				(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
			{
				// Remove when complete
				FScopeLock Lock(&InFlightQueryMutex);
				InFlightQueryResponses.Remove(StoredRequest);
			});

			AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]
			{
				HttpRequest->ProcessRequest();
			});
		}
	}
}

void UDruidsSageChatRequest_v2::ProcessPythonCompileRequests(const TArray<TSharedPtr<FJsonValue>>* PythonCompileRequestsArray)
{
	FString ResponseUrl;
	TArray<TSharedPtr<FJsonObject>> CompileResponses;

	/**
	 * Process the pending Python compile requests
	 */
	for (auto Iterator = PythonCompileRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> CompileRequestObj = (*Iterator)->AsObject(); CompileRequestObj.IsValid())
		{
			if (FString RequestId; CompileRequestObj->TryGetStringField(TEXT("request_id"), RequestId))
			{
				if (!QueryResponsesDone.Contains(RequestId))
				{
					// Extract Python code and optional GUID
					FString PythonCode;
					FString ExistingGuid;
					CompileRequestObj->TryGetStringField(TEXT("python_code"), PythonCode);
					CompileRequestObj->TryGetStringField(TEXT("existing_guid"), ExistingGuid);

					// Compile the Python code
					USagePythonWorker* Worker = USagePythonWorker::GetInstance();
					FSagePythonCompileResult CompileResult = Worker->CompilePythonCode(PythonCode, ExistingGuid);

					// Create response object
					TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
					ResponseObj->SetStringField(TEXT("request_id"), RequestId);
					ResponseObj->SetStringField(TEXT("code_guid"), CompileResult.CodeGuid);
					ResponseObj->SetBoolField(TEXT("success"), CompileResult.bSuccess);
					ResponseObj->SetStringField(TEXT("error_message"), CompileResult.ErrorMessage);
					ResponseObj->SetStringField(TEXT("error_details"), CompileResult.ErrorDetails);

					// Add the response object to the original request for sending back
					CompileRequestObj->SetObjectField(TEXT("results"), ResponseObj);

					QueryResponsesDone.Add(RequestId);
					CompileResponses.Add(CompileRequestObj);

					if (FString ResponseUrlToUse; ResponseUrl.IsEmpty() && CompileRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						ResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up compile responses and send back
	 */
	if (!CompileResponses.IsEmpty() && !ResponseUrl.IsEmpty())
	{
		SendPythonResponseToUrl(CompileResponses, ResponseUrl, TEXT("python_compile_responses"));
	}
}

void UDruidsSageChatRequest_v2::ProcessPythonExecuteRequests(const TArray<TSharedPtr<FJsonValue>>* PythonExecuteRequestsArray)
{
	FString ResponseUrl;
	TArray<TSharedPtr<FJsonObject>> ExecuteResponses;

	/**
	 * Process the pending Python execute requests
	 */
	for (auto Iterator = PythonExecuteRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> ExecuteRequestObj = (*Iterator)->AsObject(); ExecuteRequestObj.IsValid())
		{
			if (FString RequestId; ExecuteRequestObj->TryGetStringField(TEXT("request_id"), RequestId))
			{
				if (!QueryResponsesDone.Contains(RequestId))
				{
					// Extract code GUID
					FString CodeGuid;
					ExecuteRequestObj->TryGetStringField(TEXT("code_guid"), CodeGuid);

					// Execute the Python code
					USagePythonWorker* Worker = USagePythonWorker::GetInstance();
					FSagePythonExecuteResult ExecuteResult = Worker->ExecutePythonCodeByGuid(CodeGuid);

					// Create response object
					TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
					ResponseObj->SetStringField(TEXT("request_id"), RequestId);
					ResponseObj->SetStringField(TEXT("code_guid"), ExecuteResult.CodeGuid);
					ResponseObj->SetBoolField(TEXT("success"), ExecuteResult.bSuccess);
					ResponseObj->SetStringField(TEXT("result"), ExecuteResult.Result);
					ResponseObj->SetStringField(TEXT("error_message"), ExecuteResult.ErrorMessage);

					// Add the response object to the original request for sending back
					ExecuteRequestObj->SetObjectField(TEXT("results"), ResponseObj);

					QueryResponsesDone.Add(RequestId);
					ExecuteResponses.Add(ExecuteRequestObj);

					if (FString ResponseUrlToUse; ResponseUrl.IsEmpty() && ExecuteRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						ResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up execute responses and send back
	 */
	if (!ExecuteResponses.IsEmpty() && !ResponseUrl.IsEmpty())
	{
		SendPythonResponseToUrl(ExecuteResponses, ResponseUrl, TEXT("python_execute_responses"));
	}
}

void UDruidsSageChatRequest_v2::SendPythonResponseToUrl(const TArray<TSharedPtr<FJsonObject>>& Responses, const FString& ResponseUrl, const FString& ResponseArrayKey)
{
	//
	// Set up the request
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> PythonHttpRequest = FHttpModule::Get().CreateRequest();
	{
		PythonHttpRequest->SetURL(ResponseUrl);
		PythonHttpRequest->SetVerb("POST");
		PythonHttpRequest->SetHeader("Content-Type", "application/json");

		// Use the DruidsUserToken as the value for the Authorization header
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
		PythonHttpRequest->SetHeader("Authorization", AuthHeader);
	}

	//
	// Store the request while it is in flight
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
	{
		FScopeLock Lock(&InFlightQueryMutex);

		// Store the request to keep it alive until completion
		StoredRequest = PythonHttpRequest;
		InFlightQueryResponses.Add(StoredRequest);
	}

	//
	// Add the Python responses
	//
	{
		// Create a JSON object to hold the Python responses
		TSharedPtr<FJsonObject> RequestBodyObj = MakeShared<FJsonObject>();

		// Create an array of Python response objects
		TArray<TSharedPtr<FJsonValue>> ResponsesArray;
		for (const TSharedPtr<FJsonObject>& ResponseObj : Responses)
		{
			ResponsesArray.Add(MakeShared<FJsonValueObject>(ResponseObj));
		}

		// Add the array to the JSON object
		RequestBodyObj->SetArrayField(ResponseArrayKey, ResponsesArray);

		// Serialize the JSON object to a string
		FString RequestBodyString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
		FJsonSerializer::Serialize(RequestBodyObj.ToSharedRef(), Writer);

		// Set the request content
		PythonHttpRequest->SetContentAsString(RequestBodyString);
	}

	//
	// Fire the request off
	//
	{
		// Add completion handler to clean up the reference when done
		PythonHttpRequest->OnProcessRequestComplete().BindLambda(
			[this, StoredRequest]
			(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
		{
			// Remove when complete
			FScopeLock Lock(&InFlightQueryMutex);
			InFlightQueryResponses.Remove(StoredRequest);
		});

		AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [PythonHttpRequest]
		{
			PythonHttpRequest->ProcessRequest();
		});
	}
}
