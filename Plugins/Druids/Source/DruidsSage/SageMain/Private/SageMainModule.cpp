#include "SageMainModule.h"

#include "ChatRequestHandler_V2.h"
#include "DruidsSageEditorModule.h"
#include "SageExtensionDelegator.h"
#include "DruidsSageChatShell.h"
#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"

// UMG includes
#include "Blueprint/UserWidget.h"
#include "Engine/World.h"
#include "Editor.h"

// Asset loading includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Engine.h"

#define LOCTEXT_NAMESPACE "FSageMainModule"

void FSageMainModule::StartupModule()
{
	// Register for post-engine init callback
	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageMainModule::OnPostEngineInit);
}

void FSageMainModule::ShutdownModule()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
		EditorModule.OnCreateChatShell.Unbind();
	}

	FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageMainModule::OnPostEngineInit()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
        
		// Bind a lambda to the OnCreateChatShell event
		EditorModule.OnCreateChatShell.BindLambda([this]() -> TObjectPtr<UDruidsSageChatShell> {
			// Create the UMG widget - we need a world context for CreateWidget
			UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
			if (!World)
			{
				return nullptr;
			}

			// Try to get ChatWidgetOverrides from SageUI module to get the custom widget class
			TSubclassOf<UDruidsSageChatShell> WidgetClass = UDruidsSageChatShell::StaticClass(); // Default fallback

			if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
			{
				if (UChatWidgetOverrides* WidgetOverrides = SageUIModule->GetChatWidgetOverrides())
				{
					if (TSubclassOf<UDruidsSageChatShell> OverrideClass = WidgetOverrides->GetChatShellWidgetClass())
					{
						WidgetClass = OverrideClass;
						UE_LOG(LogTemp, Log, TEXT("Using custom ChatShell widget class: %s"), *WidgetClass->GetName());
					}
					else
					{
						UE_LOG(LogTemp, Log, TEXT("ChatWidgetOverrides found but no custom class specified, using default"));
					}
				}
				else
				{
					UE_LOG(LogTemp, Log, TEXT("No ChatWidgetOverrides found, using default UDruidsSageChatShell"));
				}
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("SageUI module not loaded, using default UDruidsSageChatShell"));
			}

			// Create the widget using the determined class
			UDruidsSageChatShell* ChatShell = CreateWidget<UDruidsSageChatShell>(World, WidgetClass);
			if (ChatShell)
			{
				ChatShell->SetChatRequestHandler(MakeShared<ChatRequestHandler_V2>());
				ChatShell->SetExtensionDelegator(MakeShared<FSageExtensionDelegator>());
			}

			return ChatShell;
		});
	}
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageMainModule, SageMain)