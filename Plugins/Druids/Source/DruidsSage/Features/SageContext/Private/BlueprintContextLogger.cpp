#include "BlueprintContextLogger.h"
#include "LogDruids.h"

void FBlueprintContextLogger::LogTabDetails(const TCHAR* Context, const TSharedPtr<SDockTab>& PreviouslyActive, const TSharedPtr<SDockTab>& NewlyActive)
{
    // Log previous tab info
    if (PreviouslyActive.IsValid())
    {
        FString PrevTabLabel = PreviouslyActive->GetTabLabel().ToString();
        FName PrevTabId = PreviouslyActive->GetLayoutIdentifier().TabType;
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s - Previous Tab - Label: %s, Type: %s"), 
            Context, *PrevTabLabel, *PrevTabId.ToString());
    }
    else
    {
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s - Previous Tab: None"), Context);
    }

    // Log new tab info
    if (NewlyActive.IsValid())
    {
        FString NewTabLabel = NewlyActive->GetTabLabel().ToString();
        FName NewTabId = NewlyActive->GetLayoutIdentifier().TabType;
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s - New Tab - Label: %s, Type: %s"), 
            Context, *NewTabLabel, *NewTabId.ToString());
    }
    else
    {
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s - New Tab: None"), Context);
    }
}