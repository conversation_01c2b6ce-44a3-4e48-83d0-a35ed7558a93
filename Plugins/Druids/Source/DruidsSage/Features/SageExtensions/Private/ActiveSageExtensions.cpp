#include "ActiveSageExtensions.h"
#include "SageExtension.h"

#include "AssetRegistry/AssetRegistryModule.h"

class IAssetRegistry;
class FAssetRegistryModule;

FActiveSageExtensions& FActiveSageExtensions::Get()
{
    if (!FActiveSageExtensionsSingleton::Get())
    {
        static FActiveSageExtensions Instance;
        FActiveSageExtensionsSingleton::Set(&Instance);
    }

    return *static_cast<FActiveSageExtensions*>(FActiveSageExtensionsSingleton::Get());
}

TArray<TWeakObjectPtr<USageExtension>> FActiveSageExtensions::GetActiveExtensionsForContext(
    const TWeakObjectPtr<> ActiveObject)
{
    FScopeLock Lock(&ExtensionsLock);

    FindAndLoadAllDruidSageExtensions();

    TArray<TWeakObjectPtr<USageExtension>> ActiveExtensions;

    TArray<TMap<FString, TWeakObjectPtr<USageExtension>>::ValueType> Extensions;
    RegisteredExtensions.GenerateValueArray(Extensions);
    for (TWeakObjectPtr Extension : Extensions)
    {
        if (Extension.IsValid())
        {
            if (Extension.Get()->Enabled)
            {
                if (Extension.Get()->MatchesContext(ActiveObject))
                {
                    ActiveExtensions.Add(Extension.Get());
                }
            }
        }
    }
    
    return ActiveExtensions;
}

TArray<TSharedPtr<FDruidsSageExtensionDefinition>> FActiveSageExtensions::GetDefinitionsFromExtensions(
    TArray<TWeakObjectPtr<USageExtension>> Extensions)
{
    FScopeLock Lock(&ExtensionsLock);

    FindAndLoadAllDruidSageExtensions();
    
    TArray<TSharedPtr<FDruidsSageExtensionDefinition>> Definitions;
    for (TWeakObjectPtr<USageExtension> Extension : Extensions)
    {
        Definitions.Add(Extension->GetExtensionDefinition());
    }

    return Definitions;
}

TWeakObjectPtr<USageExtension> FActiveSageExtensions::GetExtensionForId(const FString& ExtensionId)
{
    FScopeLock Lock(&ExtensionsLock);

    FindAndLoadAllDruidSageExtensions();
    
    if (const TMap<FString, TWeakObjectPtr<USageExtension>>::ValueType* Extension =
        RegisteredExtensions.Find(ExtensionId); Extension && Extension->IsValid())
    {
        return Extension->Get();
    }
    
    return nullptr;
}

void FActiveSageExtensions::Reset()
{
    FScopeLock Lock(&ExtensionsLock);
    RegisteredExtensions.Empty();
}

void FActiveSageExtensions::FindAndLoadAllDruidSageExtensions()
{
    RegisteredExtensions.Reset();

    // Get the asset registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Make sure the registry is ready
    if (!AssetRegistry.IsLoadingAssets())
    {
        // Search for EditorUtilityBlueprints
        FARFilter Filter;
        Filter.ClassPaths.Add(FTopLevelAssetPath(TEXT("/Script/Blutility"), TEXT("EditorUtilityBlueprint")));
        Filter.bRecursiveClasses = true;
        Filter.bIncludeOnlyOnDiskAssets = false;

        TArray<FAssetData> AssetDataList;
        AssetRegistry.GetAssets(Filter, AssetDataList);

        for (const FAssetData& AssetData : AssetDataList)
        {
            FString ParentClassPath;
            if (!AssetData.GetTagValue(FName("ParentClass"), ParentClassPath))
            {
                continue;
            }

            // Load parent class
            UObject* ParentClassPackage = LoadObject<UObject>(nullptr, *ParentClassPath);
            UClass* ParentClass = Cast<UClass>(ParentClassPackage);

            if (ParentClass && ParentClass->IsChildOf(USageExtension::StaticClass()))
            {
                UBlueprint* Blueprint = Cast<UBlueprint>(AssetData.GetAsset());
                if (Blueprint && Blueprint->GeneratedClass)
                {
                    USageExtension* Extension = NewObject<USageExtension>(
                        GetTransientPackage(),
                        Blueprint->GeneratedClass
                    );

                    RegisteredExtensions.Add(Extension->GetExtensionDefinition()->ExtensionId, Extension);
                }
            }
        }
    }
}