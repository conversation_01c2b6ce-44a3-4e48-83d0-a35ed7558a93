#include "SagePythonWorker.h"
#include "Engine/Engine.h"
#include "IPythonScriptPlugin.h"
#include "Modules/ModuleManager.h"
#include "PythonScriptTypes.h"

DEFINE_LOG_CATEGORY_STATIC(LogSagePython, Log, All);

USagePythonWorker* USagePythonWorker::Instance = nullptr;

USagePythonWorker::USagePythonWorker()
{
}

USagePythonWorker* USagePythonWorker::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<USagePythonWorker>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

FSagePythonCompileResult USagePythonWorker::CompilePythonCode(const FString& PythonCode, const FString& ExistingGuid)
{
    FScopeLock Lock(&CodeMapMutex);
    
    FSagePythonCompileResult Result;
    
    // Validate input
    if (PythonCode.IsEmpty())
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python code cannot be empty");
        return Result;
    }

    // Determine GUID to use
    FString UseGuid = ExistingGuid;
    if (UseGuid.IsEmpty() || !StoredPythonCode.Contains(UseGuid))
    {
        UseGuid = FGuid::NewGuid().ToString();
    }

    // Validate Python syntax
    FString ErrorMessage;
    if (!ValidatePythonSyntax(PythonCode, ErrorMessage))
    {
        Result.CodeGuid = UseGuid;
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python syntax error");
        Result.ErrorDetails = ErrorMessage;
        
        // Still store the code entry but mark as not compiled
        FSagePythonCodeEntry CodeEntry(PythonCode);
        CodeEntry.CodeGuid = UseGuid;
        CodeEntry.bIsCompiled = false;
        CodeEntry.CompilationError = ErrorMessage;
        StoredPythonCode.Add(UseGuid, CodeEntry);
        
        return Result;
    }

    // Create or update code entry
    FSagePythonCodeEntry CodeEntry(PythonCode);
    CodeEntry.CodeGuid = UseGuid;
    CodeEntry.bIsCompiled = true;
    CodeEntry.CompilationError.Empty();
    
    if (StoredPythonCode.Contains(UseGuid))
    {
        CodeEntry.CreatedTime = StoredPythonCode[UseGuid].CreatedTime;
    }
    
    StoredPythonCode.Add(UseGuid, CodeEntry);

    Result.CodeGuid = UseGuid;
    Result.bSuccess = true;
    
    UE_LOG(LogSagePython, Log, TEXT("Successfully compiled Python code with GUID: %s"), *UseGuid);
    
    return Result;
}

FSagePythonExecuteResult USagePythonWorker::ExecutePythonCodeByGuid(const FString& CodeGuid)
{
    FScopeLock Lock(&CodeMapMutex);
    
    FSagePythonExecuteResult Result;
    Result.CodeGuid = CodeGuid;
    
    // Check if GUID exists
    if (!StoredPythonCode.Contains(CodeGuid))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("No Python code found for GUID: %s"), *CodeGuid);
        return Result;
    }

    const FSagePythonCodeEntry& CodeEntry = StoredPythonCode[CodeGuid];
    
    // Check if code is compiled
    if (!CodeEntry.bIsCompiled)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python code is not compiled successfully");
        return Result;
    }

    // Execute the Python code
    FString ExecutionResult;
    FString ErrorMessage;
    
    if (ExecutePythonCode(CodeEntry.PythonCode, ExecutionResult, ErrorMessage))
    {
        Result.bSuccess = true;
        Result.Result = ExecutionResult;
        UE_LOG(LogSagePython, Log, TEXT("Successfully executed Python code with GUID: %s"), *CodeGuid);
    }
    else
    {
        Result.bSuccess = false;
        Result.ErrorMessage = ErrorMessage;
        UE_LOG(LogSagePython, Warning, TEXT("Failed to execute Python code with GUID: %s, Error: %s"), *CodeGuid, *ErrorMessage);
    }
    
    return Result;
}

FString USagePythonWorker::GetPythonCodeByGuid(const FString& CodeGuid)
{
    FScopeLock Lock(&CodeMapMutex);
    
    if (StoredPythonCode.Contains(CodeGuid))
    {
        return StoredPythonCode[CodeGuid].PythonCode;
    }
    
    return FString();
}

bool USagePythonWorker::HasPythonCodeGuid(const FString& CodeGuid)
{
    FScopeLock Lock(&CodeMapMutex);
    return StoredPythonCode.Contains(CodeGuid);
}

bool USagePythonWorker::RemovePythonCodeByGuid(const FString& CodeGuid)
{
    FScopeLock Lock(&CodeMapMutex);
    return StoredPythonCode.Remove(CodeGuid) > 0;
}

TArray<FString> USagePythonWorker::GetAllCodeGuids()
{
    FScopeLock Lock(&CodeMapMutex);
    
    TArray<FString> Guids;
    StoredPythonCode.GetKeys(Guids);
    return Guids;
}

void USagePythonWorker::ClearAllPythonCode()
{
    FScopeLock Lock(&CodeMapMutex);
    StoredPythonCode.Empty();
}

bool USagePythonWorker::ValidatePythonSyntax(const FString& PythonCode, FString& OutErrorMessage)
{
    // Get the Python Script Plugin
    IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin");
    if (!PythonPlugin)
    {
        OutErrorMessage = TEXT("Python Script Plugin not available");
        return false;
    }

    // Try to compile the Python code using Python's compile() function
    // We wrap this in a try/except to capture the detailed error information
    FString ValidationCode = FString::Printf(TEXT(
        "try:\n"
        "    compile(r'''%s''', '<string>', 'exec')\n"
        "    print('SYNTAX_OK')\n"
        "except SyntaxError as e:\n"
        "    print(f'SYNTAX_ERROR: {e}')\n"
        "except Exception as e:\n"
        "    print(f'COMPILE_ERROR: {e}')\n"
    ), *PythonCode.Replace(TEXT("'''"), TEXT("\\'\\'\\'")));

    // Set up the Python command to capture output
    FPythonCommandEx PythonCommand;
    PythonCommand.Command = ValidationCode;
    PythonCommand.ExecutionMode = EPythonCommandExecutionMode::ExecuteStatement;
    PythonCommand.Flags = EPythonCommandFlags::None;

    bool bSuccess = PythonPlugin->ExecPythonCommandEx(PythonCommand);

    if (bSuccess && PythonCommand.CommandResult.Contains(TEXT("SYNTAX_OK")))
    {
        return true;
    }
    else
    {
        // Extract the detailed error message
        if (PythonCommand.CommandResult.Contains(TEXT("SYNTAX_ERROR:")))
        {
            OutErrorMessage = PythonCommand.CommandResult.Replace(TEXT("SYNTAX_ERROR: "), TEXT(""));
        }
        else if (PythonCommand.CommandResult.Contains(TEXT("COMPILE_ERROR:")))
        {
            OutErrorMessage = PythonCommand.CommandResult.Replace(TEXT("COMPILE_ERROR: "), TEXT(""));
        }
        else
        {
            OutErrorMessage = PythonCommand.CommandResult.IsEmpty() ? TEXT("Python syntax validation failed") : PythonCommand.CommandResult;
        }

        // Clean up the error message (remove extra whitespace/newlines)
        OutErrorMessage = OutErrorMessage.TrimStartAndEnd();

        return false;
    }
}

bool USagePythonWorker::ExecutePythonCode(const FString& PythonCode, FString& OutResult, FString& OutErrorMessage)
{
    // Get the Python Script Plugin
    IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin");
    if (!PythonPlugin)
    {
        OutErrorMessage = TEXT("Python Script Plugin not available");
        return false;
    }

    // Set up the Python command
    FPythonCommandEx PythonCommand;
    PythonCommand.Command = PythonCode;
    PythonCommand.ExecutionMode = EPythonCommandExecutionMode::ExecuteStatement;
    PythonCommand.Flags = EPythonCommandFlags::None;

    // Execute the Python code using Unreal's Python integration
    bool bSuccess = PythonPlugin->ExecPythonCommandEx(PythonCommand);

    if (bSuccess)
    {
        OutResult = PythonCommand.CommandResult;
    }
    else
    {
        OutErrorMessage = PythonCommand.CommandResult.IsEmpty() ? TEXT("Python execution failed") : PythonCommand.CommandResult;
        OutResult.Empty(); // Clear result on failure
    }

    return bSuccess;
}


