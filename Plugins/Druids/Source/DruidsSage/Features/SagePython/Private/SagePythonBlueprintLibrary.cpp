#include "SagePythonBlueprintLibrary.h"
#include "SagePythonWorker.h"

FSagePythonExecuteResult USagePythonBlueprintLibrary::ExecutePythonCodeByGuid(const FString& CodeGuid)
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->ExecutePythonCodeByGuid(CodeGuid);
    }
    
    FSagePythonExecuteResult Result;
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Failed to get Python worker instance");
    return Result;
}

FSagePythonCompileResult USagePythonBlueprintLibrary::CompilePythonCode(const FString& PythonCode, const FString& ExistingGuid)
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->CompilePythonCode(PythonCode, ExistingGuid);
    }
    
    FSagePythonCompileResult Result;
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Failed to get Python worker instance");
    return Result;
}

FString USagePythonBlueprintLibrary::GetPythonCodeByGuid(const FString& CodeGuid)
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->GetPythonCodeByGuid(CodeGuid);
    }
    
    return FString();
}

bool USagePythonBlueprintLibrary::HasPythonCodeGuid(const FString& CodeGuid)
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->HasPythonCodeGuid(CodeGuid);
    }
    
    return false;
}

bool USagePythonBlueprintLibrary::RemovePythonCodeByGuid(const FString& CodeGuid)
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->RemovePythonCodeByGuid(CodeGuid);
    }
    
    return false;
}

TArray<FString> USagePythonBlueprintLibrary::GetAllCodeGuids()
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        return Worker->GetAllCodeGuids();
    }
    
    return TArray<FString>();
}

void USagePythonBlueprintLibrary::ClearAllPythonCode()
{
    USagePythonWorker* Worker = USagePythonWorker::GetInstance();
    if (Worker)
    {
        Worker->ClearAllPythonCode();
    }
}
