// Copyright Epic Games, Inc. All Rights Reserved.

#pragma warning disable 1591 // Disable documentation warning for this file

namespace EpicGames.UHT.Utils
{
	/// <summary>
	/// Collection of common name strings
	/// </summary>
	public static class UhtNames
	{
		public const string AdvancedClassDisplay = "AdvancedClassDisplay";
		public const string AdvancedDisplay = "AdvancedDisplay";
		public const string AllowPrivateAccess = "AllowPrivateAccess";
		public const string ArraySizeEnum = "ArraySizeEnum";
		public const string AutoCollapseCategories = "AutoCollapseCategories";
		public const string AutoExpandCategories = "AutoExpandCategories";
		public const string BlueprintGetter = "BlueprintGetter";
		public const string BlueprintInternalUseOnly = "BlueprintInternalUseOnly";
		public const string BlueprintSetter = "BlueprintSetter";
		public const string BlueprintType = "BlueprintType";
		public const string CannotImplementInterfaceInBlueprint = "CannotImplementInterfaceInBlueprint";
		public const string Category = "Category";
		public const string ClassGroupNames = "ClassGroupNames";
		public const string Comment = "Comment";
		public const string Constant = "Constant";
		public const string CppFromBpEvent = "CppFromBpEvent";
		public const string CustomThunk = "CustomThunk";
		public const string DeprecatedFunction = "DeprecatedFunction";
		public const string DisplayName = "DisplayName";
		public const string DocumentationPolicy = "DocumentationPolicy";
		public const string EditorConfig = "EditorConfig";
		public const string EditInline = "EditInline";
		public const string EnumDisplayNameFn = "EnumDisplayNameFn";
		public const string ExposeOnSpawn = "ExposeOnSpawn";
		public const string GetByRef = "GetByRef";
		public const string Hidden = "Hidden";
		public const string HideCategories = "HideCategories";
		public const string HideFunctions = "HideFunctions";
		public const string IgnoreCategoryKeywordsInSubclasses = "IgnoreCategoryKeywordsInSubclasses";
		public const string IncludePath = "IncludePath";
		public const string Input = "Input";
		public const string Interface = "Interface";
		public const string IsBlueprintBase = "IsBlueprintBase";
		public const string IsConversionRoot = "IsConversionRoot";
		public const string ModuleRelativePath = "ModuleRelativePath";
		public const string Name = "Name";
		public const string NativeConst = "NativeConst";
		public const string NativeConstTemplateArg = "NativeConstTemplateArg";
		public const string NoGetter = "NoGetter";
		public const string ObjectInitializerConstructorDeclared = "ObjectInitializerConstructorDeclared";
		public const string Output = "Output";
		public const string Pins = "Pins";
		public const string PrioritizeCategories = "PrioritizeCategories";
		public const string ReturnValue = "ReturnValue";
		public const string ShortToolTip = "ShortToolTip";
		public const string ShowCategories = "ShowCategories";
		public const string ShowFunctions = "ShowFunctions";
		public const string SparseClassDataTypes = "SparseClassDataTypes";
		public const string ToolTip = "ToolTip";
		public const string UIMin = "UIMin";
		public const string UIMax = "UIMax";
		public const string Visible = "Visible";
	}
}
